// Global variables
let authToken = null;
let currentCheckoutId = null;
const BASE_URL = 'http://127.0.0.1:8024'; // Update for production
const HYPERPAY_BASE_URL = 'https://test.oppwa.com/';

window.wpwlOptions = {
    style: "card",
    locale: "en",
    onReady: function() {
        console.log("Widget ready");
        document.getElementById('response').textContent += "\nPayment widget loaded successfully!";

        // Add custom styling to the pay button
        setTimeout(() => {
            const payButton = document.querySelector('.wpwl-button-pay');
            if (payButton) {
                payButton.classList.add('custom-pay-button');
                console.log("Pay button found and styled");
            }
        }, 100);
    },
    onError: function(error) {
        console.error("Widget error:", error);
        document.getElementById('response').textContent +=
            "\nWidget error: " + JSON.stringify(error, null, 2);

        // Specific error handling
        if (error.message && error.message.includes('entity type')) {
            document.getElementById('response').textContent +=
                "\n\nError: Invalid entity configuration. Please check your HyperPay setup.";
        }

        // Check for CSP errors
        if (error.message && (error.message.includes('Refused to frame') ||
            error.message.includes('Content Security Policy'))) {
            document.getElementById('response').textContent +=
                "\n\nError: Security policy blocking widget. CSP has been updated, please refresh the page.";
        }

        // Handle 403 errors
        if (error.message && error.message.includes('403')) {
            document.getElementById('response').textContent +=
                "\n\nError: Access forbidden. Please check your HyperPay credentials and entity configuration.";
        }
    },
    brandDetection: true,
    brandDetectionType: 'binlist',
    acceptedCards: ['VISA', 'MASTER', 'AMEX'],
    onBeforeSubmitCard: function() {
        console.log("About to submit payment");
        document.getElementById('response').textContent += "\nProcessing payment...";
    }
};

// Login form handler
document.getElementById('login-form').addEventListener('submit', async (event) => {
    event.preventDefault();
    document.getElementById('response').textContent = "Logging in...";

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        const response = await fetch('http://127.0.0.1:8020/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                'username': username,
                'password': password
            })
        });

        const data = await response.json();
        document.getElementById('response').textContent = JSON.stringify(data, null, 2);

        if (response.ok && data.access_token) {
            authToken = data.access_token;
            document.getElementById('checkout-button').disabled = false;
            document.getElementById('response').textContent += '\n\nLogin successful! You can now prepare checkout.';
        } else {
            throw new Error(data.detail || 'Login failed');
        }
    } catch (error) {
        document.getElementById('response').textContent = `Error: ${error.message}`;
    }
});

// Checkout preparation handler
document.getElementById('prepare-checkout').addEventListener('submit', async (event) => {
    event.preventDefault();
    document.getElementById('response').textContent = "Preparing checkout...";
    document.getElementById('card-form').innerHTML = '<p class="loading">Preparing payment form...</p>';

    if (!authToken) {
        document.getElementById('response').textContent = 'Error: Please login first!';
        return;
    }

    const payload = {
        plan_id: document.getElementById('plan_id').value,
        subscription_duration: document.getElementById('subscription_duration').value,
        billing_information: {
            email_address: document.getElementById('email').value,
            first_name: document.getElementById('first_name').value,
            last_name: document.getElementById('last_name').value,
            street_address: document.getElementById('street_address').value,
            city: document.getElementById('city').value,
            state: document.getElementById('state').value,
            post_code: document.getElementById('post_code').value,
            country: document.getElementById('country').value
        },
        coupon_code: document.getElementById('coupon_code').value || undefined
    };
    console.log('Payload:', payload);

    try {
        const response = await fetch(`${BASE_URL}/payment/create-checkout`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
            },
            body: JSON.stringify(payload),
        });

        const data = await response.json();
        document.getElementById('response').textContent = JSON.stringify(data, null, 2);

        if (response.ok && data.checkoutId) {
            currentCheckoutId = data.checkoutId;

            // Validate checkout ID format
            document.getElementById('response').textContent += '\n\n✅ Checkout prepared successfully!';
            document.getElementById('response').textContent += `\n📋 Checkout ID: ${data.checkoutId}`;
            document.getElementById('response').textContent += `\n📏 ID Length: ${data.checkoutId.length} characters`;

            // Check if checkout ID looks valid (HyperPay IDs are typically 32+ characters)
            if (data.checkoutId.length < 20) {
                document.getElementById('response').textContent +=
                    '\n⚠️  Warning: Checkout ID seems unusually short. This might cause 403 errors.';
            }

            // Additional validation
            if (!/^[A-Za-z0-9._-]+$/.test(data.checkoutId)) {
                document.getElementById('response').textContent +=
                    '\n⚠️  Warning: Checkout ID contains invalid characters.';
            }

            document.getElementById('response').textContent += '\n\n🔄 Loading payment form...';
            loadPaymentWidget(data.checkoutId);
        } else {
            throw new Error(data.detail || 'Checkout preparation failed');
        }
    } catch (error) {
        document.getElementById('response').textContent = `Error: ${error.message}`;
        document.getElementById('card-form').innerHTML = `<p class="error">Failed to prepare checkout: ${error.message}</p>`;
    }
});

// Load payment widget with all customizations
// Enhanced loadPaymentWidget function with 403 error handling
function loadPaymentWidget(checkoutId) {
    console.log("Loading payment widget with checkoutId:", checkoutId);

    // Validate checkoutId format
    if (!checkoutId || checkoutId.length < 10) {
        document.getElementById('response').textContent +=
            "\nError: Invalid checkout ID format. Please check your backend configuration.";
        return;
    }

    // Clear previous widget if exists
    const oldForm = document.querySelector('form.paymentWidgets');
    const oldScript = document.querySelector('script[src*="paymentWidgets.js"]');
    if (oldForm) {
        oldForm.remove();
        console.log("Removed old payment form");
    }
    if (oldScript) {
        oldScript.remove();
        console.log("Removed old payment script");
    }

    // Create payment form
    const form = document.createElement('form');
    form.action = `${BASE_URL}/payment/result`;
    form.className = 'paymentWidgets';
    form.dataset.brands = 'VISA MASTER AMEX DISCOVER CARTEBANCAIRE';

    // Create container for the form
    const widgetContainer = document.getElementById('card-form');
    widgetContainer.innerHTML = '<p class="loading">Loading payment form...</p>';
    widgetContainer.appendChild(form);

    // Add debugging information
    console.log("Widget URL:", `https://test.oppwa.com/v1/paymentWidgets.js?checkoutId=${checkoutId}`);
    console.log("Current origin:", window.location.origin);
    console.log("Current referrer:", document.referrer);

    // Load the widget script with enhanced error handling
    const script = document.createElement('script');
    script.src = `https://test.oppwa.com/v1/paymentWidgets.js?checkoutId=${checkoutId}`;
    script.async = true;

    script.onload = function() {
        console.log("Payment widget script loaded successfully");
        document.getElementById('response').textContent +=
            "\nPayment widget script loaded. Initializing form...";

        // Add additional debugging for widget initialization
        setTimeout(() => {
            const widgetForm = document.querySelector('.paymentWidgets');
            if (widgetForm && widgetForm.innerHTML.trim() === '') {
                document.getElementById('response').textContent +=
                    "\nWarning: Widget form is empty. This might indicate a 403 error or invalid entity configuration.";

                // Try to detect 403 errors in the widget iframe
                const iframes = document.querySelectorAll('iframe');
                iframes.forEach((iframe, index) => {
                    console.log(`Iframe ${index}:`, iframe.src);
                    iframe.onerror = function() {
                        document.getElementById('response').textContent +=
                            `\nIframe ${index} failed to load: ${iframe.src}`;
                    };
                });
            }
        }, 2000);
    };

    script.onerror = function(error) {
        console.error("Failed to load payment widget script:", error);
        document.getElementById('response').textContent +=
            "\n❌ Failed to load payment widget script.";
        document.getElementById('response').textContent +=
            "\n\nPossible causes:";
        document.getElementById('response').textContent +=
            "\n1. Invalid checkout ID from backend";
        document.getElementById('response').textContent +=
            "\n2. Incorrect HyperPay entity configuration";
        document.getElementById('response').textContent +=
            "\n3. Domain not whitelisted in HyperPay account";
        document.getElementById('response').textContent +=
            "\n4. Network connectivity issues";
        document.getElementById('response').textContent +=
            "\n\nDebugging info:";
        document.getElementById('response').textContent +=
            `\nCheckout ID: ${checkoutId}`;
        document.getElementById('response').textContent +=
            `\nWidget URL: https://test.oppwa.com/v1/paymentWidgets.js?checkoutId=${checkoutId}`;

        widgetContainer.innerHTML = '<p class="error">Failed to load payment form. Check console for details.</p>';
    };

    document.head.appendChild(script);
}


// Diagnostic function to test HyperPay connectivity
async function testHyperPayConnectivity() {
    document.getElementById('response').textContent += "\n\n🔍 Testing HyperPay connectivity...";

    try {
        // Test basic connectivity to HyperPay
        const testResponse = await fetch('https://test.oppwa.com/', {
            method: 'HEAD',
            mode: 'no-cors'
        });
        document.getElementById('response').textContent += "\n✅ HyperPay domain is reachable";
    } catch (error) {
        document.getElementById('response').textContent += "\n❌ Cannot reach HyperPay domain: " + error.message;
    }

    // Check if we have a valid checkout ID
    if (currentCheckoutId) {
        document.getElementById('response').textContent += `\n📋 Current checkout ID: ${currentCheckoutId}`;
        document.getElementById('response').textContent += `\n📏 Checkout ID length: ${currentCheckoutId.length}`;

        // Test the widget URL directly
        const widgetUrl = `https://test.oppwa.com/v1/paymentWidgets.js?checkoutId=${currentCheckoutId}`;
        document.getElementById('response').textContent += `\n🔗 Widget URL: ${widgetUrl}`;

        try {
            const widgetResponse = await fetch(widgetUrl, { method: 'HEAD', mode: 'no-cors' });
            document.getElementById('response').textContent += "\n✅ Widget URL is accessible";
        } catch (error) {
            document.getElementById('response').textContent += "\n❌ Widget URL failed: " + error.message;
        }
    } else {
        document.getElementById('response').textContent += "\n❌ No checkout ID available";
    }
}

// Add a test button to the page
document.addEventListener('DOMContentLoaded', function() {
    const testButton = document.createElement('button');
    testButton.textContent = 'Test HyperPay Connectivity';
    testButton.onclick = testHyperPayConnectivity;
    testButton.style.margin = '10px';
    testButton.style.padding = '10px';
    testButton.style.backgroundColor = '#007bff';
    testButton.style.color = 'white';
    testButton.style.border = 'none';
    testButton.style.borderRadius = '5px';
    testButton.style.cursor = 'pointer';

    const container = document.querySelector('.container');
    container.insertBefore(testButton, container.firstChild);
});

// Handle payment completion
window.addEventListener('paymentCompleted', function(event) {
    document.getElementById('response').textContent +=
        "\n\nPayment completed:\n" + JSON.stringify(event.detail, null, 2);
});

// Handle payment failed
window.addEventListener('paymentFailed', function(event) {
    document.getElementById('response').textContent +=
        "\n\nPayment failed:\n" + JSON.stringify(event.detail, null, 2);
});

// Monitor for 403 errors in the console
const originalConsoleError = console.error;
console.error = function(...args) {
    const errorMessage = args.join(' ');
    if (errorMessage.includes('403') || errorMessage.includes('Access Denied')) {
        document.getElementById('response').textContent +=
            "\n🚨 403 Error detected: " + errorMessage;
        document.getElementById('response').textContent +=
            "\n\nThis usually means:";
        document.getElementById('response').textContent +=
            "\n- Invalid entity ID in your HyperPay account";
        document.getElementById('response').textContent +=
            "\n- Domain not whitelisted in HyperPay settings";
        document.getElementById('response').textContent +=
            "\n- Incorrect test/production environment";
    }
    originalConsoleError.apply(console, args);
};
