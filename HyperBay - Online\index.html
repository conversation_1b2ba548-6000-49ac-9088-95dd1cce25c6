<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy"
    content="
    default-src 'self';
    style-src 'self' https://eu-prod.oppwa.com/ 'unsafe-inline';
    frame-src 'self' https://eu-prod.oppwa.com/ https://oppwa.com/ 
               https://geoissuer.cardinalcommerce.com/ 
               https://eu-prod.ppipe.net/
               https://authentication.cardinalcommerce.com/;
    script-src 'self' https://eu-prod.oppwa.com/ https://oppwa.com/ 
               'nonce-123456789' 'unsafe-eval' blob:;
    connect-src 'self' https://eu-prod.oppwa.com/ https://oppwa.com/ 
                http://127.0.0.1:8020 http://127.0.0.1:8024 
                http://localhost:8024 http://localhost:8020
                https://overbridgenet.com/;
    img-src 'self' https://eu-prod.oppwa.com/ data:;
    child-src blob:;
    ">
    <title>HyperPay Secure Integration</title>
    <link rel="stylesheet" href="styles.css">
    
    <!-- HyperPay Widget Configuration -->
    <script nonce="123456789">
        window.wpwlOptions = {
            style: "card",
            locale: "en",
            paymentTarget: '_top',
            
            // Custom placeholder styles
            iframeStyles: {
                'card-number-placeholder': {
                    'color': '#7f8c8d',
                    'font-size': '15px',
                    'font-family': 'Arial'
                },
                'cvv-placeholder': {
                    'color': '#7f8c8d',
                    'font-size': '15px',
                    'font-family': 'Arial'
                }
            },
            
            // Event handlers
            onReady: function() {
                console.log("Payment widget ready");
                document.getElementById('response').textContent += "\nWidget loaded successfully";
                
                // Add custom class to the pay button
                var payButton = document.querySelector('.wpwl-button-pay');
                if (payButton) {
                    payButton.classList.add('custom-pay-button');
                }
            },
            onError: function(error) {
                console.error("Widget error:", error);
                document.getElementById('response').textContent += 
                    "\nWidget error: " + JSON.stringify(error, null, 2);
                
                if (error.message.includes('entity type')) {
                    document.getElementById('response').textContent += 
                        "\n\nPlease verify your backend is using the correct entityId";
                }
            },
            
            // Customize the pay button text
            labels: {
                submit: "Pay Now"
            }
        };
    </script>
</head>
<body>
    <div class="container">
        <h1>HyperPay Payment Integration</h1>
        
        <!-- Login Section -->
        <div class="step">
            <h2>Step 1: Login</h2>
            <form id="login-form">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="<EMAIL>" required>

                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="123456" required>

                <button type="submit">Login</button>
            </form>
        </div>

        <!-- Checkout Preparation -->
        <div class="step">
            <h2>Step 2: Prepare Checkout</h2>
            <form id="prepare-checkout">
                <label for="plan_type">Plan Type:</label>
                <input type="text" id="plan_id" name="plan_id" value="29f90aea-1c3a-45f5-9dd2-2755d3bd705c" required>

                <label for="subscription_duration">Subscription Duration:</label>
                <select id="subscription_duration" name="subscription_duration" required>
                    <option value="MONTHLY">Monthly</option>
                    <option value="ANNUAL">Annual</option>
                </select>

                <h3>Billing Information</h3>
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>

                <label for="first_name">First Name:</label>
                <input type="text" id="first_name" name="first_name" value="Nayer" required>

                <label for="last_name">Last Name:</label>
                <input type="text" id="last_name" name="last_name" value="Nasef" required>

                <label for="street_address">Street Address:</label>
                <input type="text" id="street_address" name="street_address" value="123 Test St" required>

                <label for="city">City:</label>
                <input type="text" id="city" name="city" value="Test City" required>

                <label for="state">State:</label>
                <input type="text" id="state" name="state" value="Test State" required>

                <label for="post_code">Postal Code:</label>
                <input type="text" id="post_code" name="post_code" value="12345" required>

                <label for="country">Country:</label>
                <input type="text" id="country" name="country" value="US" required>

                <label for="coupon_code">Coupon Code (optional):</label>
                <input type="text" id="coupon_code" name="coupon_code">

                <button type="submit" disabled id="checkout-button">Prepare Checkout</button>
            </form>
        </div>

        <!-- Payment Widget -->
        <div class="step">
            <h2>Step 3: Payment</h2>
            <div id="payment-container">
                <div class="payment-form">
                    <h3>Credit/Debit Cards</h3>
                    <div id="card-form">
                        <p class="loading">Payment form will appear here after checkout preparation</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Response Console -->
        <div class="step">
            <h2>Response</h2>
            <div id="response">Waiting for actions...</div>
        </div>
    </div>

    <script src="script.js" nonce="123456789"></script>
</body>
</html>
