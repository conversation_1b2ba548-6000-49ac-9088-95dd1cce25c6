/* Base Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 20px;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}
.step {
    margin-bottom: 30px;
    padding: 25px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
h1, h2 {
    color: #2c3e50;
    margin-top: 0;
}

/* Form Styles */
label {
    display: block;
    margin: 15px 0 8px;
    font-weight: bold;
    color: #34495e;
}
input, select {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-sizing: border-box;
    max-width: 500px;
    font-size: 15px;
    transition: border-color 0.3s;
}
input:focus, select:focus {
    border-color: #3498db;
    outline: none;
}
button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s;
    font-weight: bold;
    margin-top: 10px;
}
button:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Payment Container */
#payment-container {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    margin-top: 25px;
}
.payment-form {
    flex: 1;
    min-width: 350px;
    border: 1px solid #e0e0e0;
    padding: 25px;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* Response Area */
#response {
    background-color: white;
    padding: 20px;
    border-radius: 6px;
    margin-top: 20px;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    font-size: 14px;
}

/* Status Messages */
.loading {
    color: #7f8c8d;
    font-style: italic;
    padding: 10px;
}
.error {
    color: #e74c3c;
    font-weight: bold;
    padding: 10px;
    background-color: #fdecea;
    border-radius: 4px;
}
.success {
    color: #27ae60;
    font-weight: bold;
    padding: 10px;
    background-color: #e8f5e9;
    border-radius: 4px;
}

/* HyperPay Widget Custom Styles */
.wpwl-container {
    margin: 15px 0;
}
.wpwl-form {
    background: transparent;
    box-shadow: none;
    padding: 0;
}
.wpwl-group {
    margin-bottom: 15px;
}
.wpwl-label {
    color: #34495e;
    font-size: 14px;
    margin-bottom: 8px;
}
.wpwl-control {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    transition: border-color 0.3s;
}
.wpwl-control:focus {
    border-color: #3498db;
    outline: none;
}
.wpwl-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
    width: 100%;
}
.wpwl-button:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.wpwl-brand {
    margin-right: 10px;
}