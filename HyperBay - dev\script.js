// Global variables
let authToken = null;
let currentCheckoutId = null;
const BASE_URL = 'https://leh-dev.theosumma.com/api/v1/subscription'; // Update for production
const HYPERPAY_BASE_URL = 'https://test.oppwa.com/';

window.wpwlOptions = {
    style: "card",
    locale: "en",
    onReady: function() {
        console.log("Widget ready");
        // Additional initialization if needed
    },
    onError: function(error) {
        console.error("Widget error:", error);
        document.getElementById('response').textContent += 
            "\nWidget error: " + JSON.stringify(error, null, 2);
        
        // Specific error handling
        if (error.message.includes('entity type')) {
            alert('Payment configuration error. Please contact support.');
        }
        
        // Check for CSP errors
        if (error.message.includes('Refused to frame') || 
            error.message.includes('Content Security Policy')) {
            alert('Security policy error. Please try refreshing the page.');
        }
    },
    brandDetection: true,
    brandDetectionType: 'binlist',  
    acceptedCards: ['VISA', 'MASTER', 'AMEX']
};

// Login form handler
document.getElementById('login-form').addEventListener('submit', async (event) => {
    event.preventDefault();
    document.getElementById('response').textContent = "Logging in...";

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        const response = await fetch('https://leh-dev.theosumma.com/api/v1/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                'username': username,
                'password': password
            })
        });

        const data = await response.json();
        document.getElementById('response').textContent = JSON.stringify(data, null, 2);

        if (response.ok && data.access_token) {
            authToken = data.access_token;
            document.getElementById('checkout-button').disabled = false;
            document.getElementById('response').textContent += '\n\nLogin successful! You can now prepare checkout.';
        } else {
            throw new Error(data.detail || 'Login failed');
        }
    } catch (error) {
        document.getElementById('response').textContent = `Error: ${error.message}`;
    }
});

// Checkout preparation handler
document.getElementById('prepare-checkout').addEventListener('submit', async (event) => {
    event.preventDefault();
    document.getElementById('response').textContent = "Preparing checkout...";
    document.getElementById('card-form').innerHTML = '<p class="loading">Preparing payment form...</p>';

    if (!authToken) {
        document.getElementById('response').textContent = 'Error: Please login first!';
        return;
    }

    const payload = {
        plan_id: document.getElementById('plan_id').value,
        subscription_duration: document.getElementById('subscription_duration').value,
        billing_information: {
            email_address: document.getElementById('email').value,
            first_name: document.getElementById('first_name').value,
            last_name: document.getElementById('last_name').value,
            street_address: document.getElementById('street_address').value,
            city: document.getElementById('city').value,
            state: document.getElementById('state').value,
            post_code: document.getElementById('post_code').value,
            country: document.getElementById('country').value
        },
        coupon_code: document.getElementById('coupon_code').value || undefined
    };
    console.log('Payload:', payload);
    try {
        const response = await fetch(`${BASE_URL}/payment/create-checkout`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
            },
            body: JSON.stringify(payload),
        });

        const data = await response.json();
        document.getElementById('response').textContent = JSON.stringify(data, null, 2);

        if (response.ok && data.checkoutId) {
            currentCheckoutId = data.checkoutId;
            document.getElementById('response').textContent += '\n\nCheckout prepared successfully! Loading payment form...';
            loadPaymentWidget(data.checkoutId);
        } else {
            throw new Error(data.detail || 'Checkout preparation failed');
        }
    } catch (error) {
        document.getElementById('response').textContent = `Error: ${error.message}`;
        document.getElementById('card-form').innerHTML = `<p class="error">Failed to prepare checkout: ${error.message}</p>`;
    }
});

// Load payment widget with all customizations
// Enhanced loadPaymentWidget function
function loadPaymentWidget(checkoutId) {
    // Clear previous widget if exists
    const oldForm = document.querySelector('form.paymentWidgets');
    const oldScript = document.querySelector('script[src*="paymentWidgets.js"]');
    if (oldForm) oldForm.remove();
    if (oldScript) oldScript.remove();

    // Create payment form
    const form = document.createElement('form');
    form.action = `${BASE_URL}/payment/result`;
    form.className = 'paymentWidgets';
    form.dataset.brands = 'VISA MASTER AMEX DISCOVER CARTEBANCAIRE';
    
    // Create container for the form
    const widgetContainer = document.getElementById('card-form');
    widgetContainer.innerHTML = '';
    widgetContainer.appendChild(form);

    // Load the widget script with error handling
    const script = document.createElement('script');
    script.src = `https://test.oppwa.com/v1/paymentWidgets.js?checkoutId=${checkoutId}`;
    script.async = true;
    script.onerror = function() {
        document.getElementById('response').textContent += 
            "\nFailed to load payment widget script. Please check your network connection.";
    };
    document.body.appendChild(script);
}


// Handle payment completion
window.addEventListener('paymentCompleted', function(event) {
    document.getElementById('response').textContent += 
        "\n\nPayment completed:\n" + JSON.stringify(event.detail, null, 2);
});

// Handle payment failed
window.addEventListener('paymentFailed', function(event) {
    document.getElementById('response').textContent += 
        "\n\nPayment failed:\n" + JSON.stringify(event.detail, null, 2);
});
